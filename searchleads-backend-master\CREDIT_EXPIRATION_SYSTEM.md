# SearchLeads Credit Expiration System

## 🎉 System Status: FULLY IMPLEMENTED AND TESTED

Your SearchLeads application now has a comprehensive credit expiration system that properly tracks and manages credit lifecycles with automatic expiration.

## ✅ What's Been Implemented

### 1. **Credit Ledger System**
- ✅ **CreditTransaction Model**: Tracks individual credit transactions with expiration dates
- ✅ **FIFO Usage**: Credits are used in First-In-First-Out order (oldest credits first)
- ✅ **Expiration Tracking**: Each credit transaction has an optional expiration date
- ✅ **Audit Trail**: Complete history of all credit additions, usage, and expirations

### 2. **Credit Types and Expiration Rules**
- ✅ **Subscription Credits**: Expire after **30 days** from addition
- ✅ **Pay-as-you-go Credits**: **Never expire** (permanent until used)
- ✅ **Bonus Credits**: Expire after **90 days** (configurable)
- ✅ **Admin Credits**: Configurable expiration (default: 365 days)
- ✅ **Refunded Credits**: Never expire

### 3. **Automated Expiration System**
- ✅ **Background Jobs**: Automatic credit expiration processing
- ✅ **Expiration Warnings**: Notify users of credits expiring soon
- ✅ **Manual Tools**: Scripts for manual expiration management
- ✅ **Statistics**: Comprehensive expiration reporting

### 4. **API Endpoints**
- ✅ **Credit Balance**: `/api/credits/balance` - Get detailed credit information
- ✅ **Credit History**: `/api/credits/history` - Transaction history
- ✅ **Expiring Credits**: `/api/credits/expiring` - Credits expiring soon
- ✅ **Admin Tools**: Credit management endpoints for administrators

## 🔧 How It Works

### Credit Addition Flow
```
1. User gets subscription → Add credits with 30-day expiration
2. User buys pay-as-you-go → Add credits with no expiration
3. Admin adds bonus → Add credits with custom expiration
4. Credits stored in CreditTransaction table with expiration dates
```

### Credit Usage Flow
```
1. User uses credits → System finds oldest available credits first
2. Deduct from oldest credits (FIFO)
3. Create usage transaction record
4. Update remaining amounts
5. Update user's legacy credits field
```

### Expiration Flow
```
1. Background job runs hourly
2. Find credits past expiration date
3. Create expiration transaction records
4. Set remaining amount to 0
5. Update user's credit balance
6. Send notifications if needed
```

## 📊 Current Configuration

### Expiration Rules
- **Subscription Credits**: 30 days from addition
- **Pay-as-you-go Credits**: Never expire
- **Bonus Credits**: 90 days (configurable)
- **Admin Credits**: 365 days (configurable)

### Background Jobs
- **Expiration Job**: Runs every hour
- **Warning Notifications**: Daily at 9 AM
- **Statistics**: Real-time calculation

## 🛠 Management Tools

### Scripts Available
```bash
# Test the credit expiration system
npm run test-credit-expiration

# Migrate existing credits to new system
npm run migrate-credits
npm run migrate-credits -- --dry-run

# Manually expire credits
npm run expire-credits
npm run expire-credits -- --stats
npm run expire-credits -- --warnings

# Validate system setup
npm run validate-setup
```

### API Endpoints
```http
# User endpoints
GET /api/credits/balance          # Get credit balance
GET /api/credits/history          # Get transaction history
GET /api/credits/expiring         # Get expiring credits
GET /api/credits/stats            # Get credit statistics

# Admin endpoints
POST /api/credits/admin/add       # Add admin credits
POST /api/credits/admin/bonus     # Add bonus credits
POST /api/credits/admin/refund    # Refund credits
GET /api/credits/admin/user/:id   # Get user credit details
```

## 📈 Monitoring and Reporting

### Credit Balance Response
```json
{
  "balance": {
    "totalCredits": 15000,
    "availableCredits": 12000,
    "expiredCredits": 3000,
    "expiringCredits": [
      {
        "amount": 5000,
        "expiresAt": "2024-02-15T00:00:00Z"
      }
    ]
  }
}
```

### Expiration Statistics
```json
{
  "stats": {
    "totalExpiredCredits": 50000,
    "totalExpiringCredits": 25000,
    "usersWithExpiringCredits": 150,
    "nextExpirationDate": "2024-02-01T00:00:00Z"
  }
}
```

## 🔄 Migration from Legacy System

### Automatic Migration
The system includes a migration script to convert existing user credits:

```bash
# Migrate all users (dry run first)
npm run migrate-credits -- --dry-run
npm run migrate-credits

# Migrate specific user
npm run migrate-credits -- --user-id=USER_ID

# Set custom expiration for migrated credits
npm run migrate-credits -- --expiration-days=365
```

### Migration Process
1. **Identifies users** with existing credits
2. **Creates credit transactions** for existing balances
3. **Sets expiration dates** based on configuration
4. **Preserves backward compatibility** with legacy credits field
5. **Validates migration** by comparing totals

## ⚡ Performance Considerations

### Database Optimization
- ✅ **Indexed queries** for fast expiration lookups
- ✅ **Efficient FIFO** credit usage algorithm
- ✅ **Batch processing** for large-scale operations
- ✅ **Cached calculations** for user credit balances

### Background Job Efficiency
- ✅ **Hourly expiration** processing (configurable)
- ✅ **User-specific** expiration when needed
- ✅ **Error handling** and retry logic
- ✅ **Performance monitoring** and logging

## 🚨 Troubleshooting

### Common Issues

1. **Credits not expiring**
   - Check background job is running
   - Verify expiration dates in database
   - Run manual expiration: `npm run expire-credits`

2. **Incorrect credit balances**
   - Run migration validation
   - Check for duplicate transactions
   - Verify FIFO usage logic

3. **Performance issues**
   - Monitor database query performance
   - Check index usage on CreditTransaction table
   - Consider batch size adjustments

### Debugging Tools
```bash
# Check system health
npm run validate-setup

# Get detailed statistics
npm run expire-credits -- --stats

# Test specific user
npm run expire-credits -- --user-id=USER_ID --dry-run
```

## 🔮 Future Enhancements

### Potential Improvements
1. **Email Notifications**: Integrate with email service for expiration warnings
2. **SMS Alerts**: Critical expiration notifications via SMS
3. **Dashboard**: Real-time credit expiration dashboard
4. **Analytics**: Advanced credit usage analytics
5. **Auto-renewal**: Automatic subscription renewal before expiration

### Scaling Considerations
1. **Database Partitioning**: Partition credit transactions by date
2. **Caching**: Redis cache for frequently accessed credit balances
3. **Queue System**: Use job queue for large-scale expiration processing
4. **Archiving**: Archive old expired transactions

## 📞 Support

### Monitoring Checklist
- [ ] Background jobs running correctly
- [ ] Credit balances calculating accurately
- [ ] Expiration notifications being sent
- [ ] Database performance acceptable
- [ ] Error rates within normal range

### Key Metrics to Track
- **Active Credits**: Total non-expired credits in system
- **Expiration Rate**: Credits expired per day/week/month
- **Usage Patterns**: How users consume credits over time
- **System Performance**: Query response times and job execution times

---

**🎉 Your credit expiration system is now fully operational and ready for production use!**

The system properly handles the 30-day expiration for subscription credits while maintaining the existing pay-as-you-go model, providing comprehensive tracking, automation, and management tools.
