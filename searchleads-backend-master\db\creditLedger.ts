import { PrismaClient, CreditTransaction, CreditTransactionType, User } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Credit Ledger Management System
 * 
 * This module handles all credit transactions with proper expiration tracking,
 * FIFO usage, and comprehensive audit trails.
 */

export interface AddCreditsOptions {
  userId: string;
  amount: number;
  type: CreditTransactionType;
  source?: string;
  sourceId?: string;
  description?: string;
  expiresAt?: Date;
}

export interface UseCreditsOptions {
  userId: string;
  amount: number;
  description?: string;
  sourceId?: string; // e.g., log ID for tracking usage
}

export interface CreditBalance {
  totalCredits: number;
  availableCredits: number; // excluding expired
  expiredCredits: number;
  expiringCredits: { amount: number; expiresAt: Date }[];
}

/**
 * Add credits to user account with expiration tracking
 */
export async function addCreditsToLedger(options: AddCreditsOptions): Promise<CreditTransaction> {
  try {
    const transaction = await prisma.creditTransaction.create({
      data: {
        userId: options.userId,
        amount: Math.abs(options.amount), // Ensure positive for additions
        type: options.type,
        source: options.source,
        sourceId: options.sourceId,
        description: options.description,
        expiresAt: options.expiresAt,
        remainingAmount: Math.abs(options.amount),
      },
    });

    // Update user's legacy credits field for backward compatibility
    await updateUserCreditBalance(options.userId);

    return transaction;
  } catch (error: any) {
    console.error('Error adding credits to ledger:', error);
    throw new Error(error.message);
  }
}

/**
 * Use credits from user account using FIFO (oldest first) logic
 */
export async function useCreditsFromLedger(options: UseCreditsOptions): Promise<{
  success: boolean;
  creditsUsed: number;
  transactions: CreditTransaction[];
}> {
  try {
    const amountToUse = Math.abs(options.amount);
    
    // Get available credits (non-expired, with remaining amount)
    const availableCredits = await prisma.creditTransaction.findMany({
      where: {
        userId: options.userId,
        type: {
          in: [
            CreditTransactionType.PURCHASE_SUBSCRIPTION,
            CreditTransactionType.PURCHASE_PAYG,
            CreditTransactionType.ADMIN_ADJUSTMENT,
            CreditTransactionType.BONUS,
            CreditTransactionType.REFUND,
          ],
        },
        remainingAmount: { gt: 0 },
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ],
      },
      orderBy: { createdAt: 'asc' }, // FIFO - oldest first
    });

    const totalAvailable = availableCredits.reduce((sum, credit) => sum + credit.remainingAmount, 0);
    
    if (totalAvailable < amountToUse) {
      return {
        success: false,
        creditsUsed: 0,
        transactions: [],
      };
    }

    const usageTransactions: CreditTransaction[] = [];
    let remainingToUse = amountToUse;

    // Use credits in FIFO order
    for (const creditTransaction of availableCredits) {
      if (remainingToUse <= 0) break;

      const amountFromThisCredit = Math.min(remainingToUse, creditTransaction.remainingAmount);
      
      // Update the credit transaction
      await prisma.creditTransaction.update({
        where: { id: creditTransaction.id },
        data: {
          remainingAmount: creditTransaction.remainingAmount - amountFromThisCredit,
        },
      });

      // Create usage record
      const usageTransaction = await prisma.creditTransaction.create({
        data: {
          userId: options.userId,
          amount: -amountFromThisCredit, // Negative for usage
          type: CreditTransactionType.USAGE,
          source: 'usage',
          sourceId: options.sourceId,
          description: options.description || 'Credit usage',
          usedAt: new Date(),
          remainingAmount: 0,
        },
      });

      usageTransactions.push(usageTransaction);
      remainingToUse -= amountFromThisCredit;
    }

    // Update user's legacy credits field
    await updateUserCreditBalance(options.userId);

    return {
      success: true,
      creditsUsed: amountToUse,
      transactions: usageTransactions,
    };
  } catch (error: any) {
    console.error('Error using credits from ledger:', error);
    throw new Error(error.message);
  }
}

/**
 * Get user's credit balance with expiration details
 */
export async function getUserCreditBalance(userId: string): Promise<CreditBalance> {
  try {
    const now = new Date();
    
    // Get all credit transactions
    const transactions = await prisma.creditTransaction.findMany({
      where: {
        userId,
        type: {
          in: [
            CreditTransactionType.PURCHASE_SUBSCRIPTION,
            CreditTransactionType.PURCHASE_PAYG,
            CreditTransactionType.ADMIN_ADJUSTMENT,
            CreditTransactionType.BONUS,
            CreditTransactionType.REFUND,
          ],
        },
      },
    });

    let totalCredits = 0;
    let availableCredits = 0;
    let expiredCredits = 0;
    const expiringCredits: { amount: number; expiresAt: Date }[] = [];

    for (const transaction of transactions) {
      totalCredits += transaction.remainingAmount;

      if (transaction.expiresAt && transaction.expiresAt <= now) {
        // Expired
        expiredCredits += transaction.remainingAmount;
      } else {
        // Available
        availableCredits += transaction.remainingAmount;
        
        if (transaction.expiresAt) {
          expiringCredits.push({
            amount: transaction.remainingAmount,
            expiresAt: transaction.expiresAt,
          });
        }
      }
    }

    // Sort expiring credits by expiration date
    expiringCredits.sort((a, b) => a.expiresAt.getTime() - b.expiresAt.getTime());

    return {
      totalCredits,
      availableCredits,
      expiredCredits,
      expiringCredits,
    };
  } catch (error: any) {
    console.error('Error getting user credit balance:', error);
    throw new Error(error.message);
  }
}

/**
 * Expire credits that have passed their expiration date
 */
export async function expireCredits(userId?: string): Promise<{
  expiredTransactions: number;
  totalExpiredCredits: number;
}> {
  try {
    const now = new Date();
    
    const whereClause: any = {
      expiresAt: { lte: now },
      remainingAmount: { gt: 0 },
      type: {
        in: [
          CreditTransactionType.PURCHASE_SUBSCRIPTION,
          CreditTransactionType.PURCHASE_PAYG,
          CreditTransactionType.ADMIN_ADJUSTMENT,
          CreditTransactionType.BONUS,
          CreditTransactionType.REFUND,
        ],
      },
    };

    if (userId) {
      whereClause.userId = userId;
    }

    // Find expired credits
    const expiredCredits = await prisma.creditTransaction.findMany({
      where: whereClause,
    });

    let totalExpiredCredits = 0;
    const expiredTransactions: CreditTransaction[] = [];

    // Process each expired credit
    for (const expiredCredit of expiredCredits) {
      if (expiredCredit.remainingAmount > 0) {
        // Create expiration record
        const expirationTransaction = await prisma.creditTransaction.create({
          data: {
            userId: expiredCredit.userId,
            amount: -expiredCredit.remainingAmount,
            type: CreditTransactionType.EXPIRATION,
            source: 'expiration',
            sourceId: expiredCredit.id,
            description: `Credits expired from ${expiredCredit.source || 'unknown source'}`,
            usedAt: now,
            remainingAmount: 0,
          },
        });

        // Update original transaction
        await prisma.creditTransaction.update({
          where: { id: expiredCredit.id },
          data: { remainingAmount: 0 },
        });

        totalExpiredCredits += expiredCredit.remainingAmount;
        expiredTransactions.push(expirationTransaction);

        // Update user's legacy credits field
        await updateUserCreditBalance(expiredCredit.userId);
      }
    }

    return {
      expiredTransactions: expiredTransactions.length,
      totalExpiredCredits,
    };
  } catch (error: any) {
    console.error('Error expiring credits:', error);
    throw new Error(error.message);
  }
}

/**
 * Update user's legacy credits field based on ledger
 */
async function updateUserCreditBalance(userId: string): Promise<void> {
  try {
    const balance = await getUserCreditBalance(userId);
    
    await prisma.user.update({
      where: { UserID: userId },
      data: { credits: balance.availableCredits },
    });
  } catch (error: any) {
    console.error('Error updating user credit balance:', error);
    // Don't throw here to avoid breaking the main operation
  }
}

/**
 * Get credit transaction history for a user
 */
export async function getCreditHistory(
  userId: string,
  limit: number = 50,
  offset: number = 0
): Promise<CreditTransaction[]> {
  try {
    return await prisma.creditTransaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
    });
  } catch (error: any) {
    console.error('Error getting credit history:', error);
    throw new Error(error.message);
  }
}
