import express, { Request, Response } from 'express';
import Stripe from 'stripe';
import { getUser } from '../db/user';
import { addSubscriptionCredits } from '../db/creditManager';
import { 
  createSubscription, 
  getSubscriptionByUserId, 
  cancelSubscription,
  addStripeCustomerId,
  getSubscriptionStats
} from '../db/subscription';
import userAuth from '../middleware/supabaseAuth';
import { stripeClient } from '../payments/stripe';
import { getSubscriptionPriceId, getAvailableSubscriptionPlans } from '../utils/stripeConfig';

const app = express.Router();

// Create subscription
app.post('/create', userAuth, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const { credits, paymentMethodId } = req.body;

    if (!credits || !paymentMethodId) {
      return res.status(400).json({ error: 'Credits and payment method are required' });
    }

    // Validate credits amount (must be multiple of 10K)
    if (credits % 10000 !== 0 || credits < 10000 || credits > 50000) {
      return res.status(400).json({ 
        error: 'Credits must be a multiple of 10,000 between 10,000 and 50,000' 
      });
    }

    const user = await getUser(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if user already has an active subscription
    const existingSubscription = await getSubscriptionByUserId(userId);
    if (existingSubscription) {
      return res.status(400).json({ 
        error: 'User already has an active subscription' 
      });
    }

    // Get the Stripe price ID for the requested credits
    const stripePriceId = await getSubscriptionPriceId(credits);
    if (!stripePriceId) {
      return res.status(400).json({
        error: 'Invalid credits amount for subscription'
      });
    }

    let stripeCustomerId = user.stripeCustomerId;

    // Create Stripe customer if doesn't exist
    if (!stripeCustomerId) {
      const customer = await stripeClient.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user.UserID,
        },
      });
      stripeCustomerId = customer.id;
      await addStripeCustomerId(userId, stripeCustomerId);
    }

    // Attach payment method to customer
    await stripeClient.paymentMethods.attach(paymentMethodId, {
      customer: stripeCustomerId,
    });

    // Set as default payment method
    await stripeClient.customers.update(stripeCustomerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    // Create subscription
    const subscription = await stripeClient.subscriptions.create({
      customer: stripeCustomerId,
      items: [{ price: stripePriceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
    });

    // Save subscription to database
    await createSubscription(
      userId,
      subscription.id,
      stripePriceId,
      subscription.status,
      new Date(subscription.current_period_start * 1000),
      new Date(subscription.current_period_end * 1000)
    );

    // Add initial subscription credits with 30-day expiration
    const creditResult = await addSubscriptionCredits(
      userId,
      credits,
      subscription.id,
      `Initial subscription credits - ${credits} credits`
    );

    if (!creditResult.success) {
      throw new Error(creditResult.error || 'Failed to add subscription credits');
    }

    const latestInvoice = subscription.latest_invoice as Stripe.Invoice;
    const paymentIntent = latestInvoice.payment_intent as Stripe.PaymentIntent;

    res.status(200).json({
      subscriptionId: subscription.id,
      clientSecret: paymentIntent.client_secret,
      status: subscription.status,
    });
  } catch (error: any) {
    console.error('Error creating subscription:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get user's subscription
app.get('/current', userAuth, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    
    const subscription = await getSubscriptionByUserId(userId);
    
    if (!subscription) {
      return res.status(404).json({ error: 'No active subscription found' });
    }

    res.status(200).json({
      subscription: {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        credits: subscription.price.credits,
        amount: subscription.price.unitAmount,
        interval: subscription.price.interval,
      },
    });
  } catch (error: any) {
    console.error('Error getting subscription:', error);
    res.status(500).json({ error: error.message });
  }
});

// Cancel subscription
app.post('/cancel', userAuth, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    
    const subscription = await getSubscriptionByUserId(userId);
    if (!subscription) {
      return res.status(404).json({ error: 'No active subscription found' });
    }

    // Cancel subscription in Stripe (at period end)
    await stripeClient.subscriptions.update(subscription.stripeSubscriptionId, {
      cancel_at_period_end: true,
    });

    // Update subscription in database
    await cancelSubscription(subscription.stripeSubscriptionId);

    res.status(200).json({ 
      message: 'Subscription will be canceled at the end of the current period',
      cancelAtPeriodEnd: true,
      currentPeriodEnd: subscription.currentPeriodEnd,
    });
  } catch (error: any) {
    console.error('Error canceling subscription:', error);
    res.status(500).json({ error: error.message });
  }
});

// Reactivate subscription
app.post('/reactivate', userAuth, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    
    const subscription = await getSubscriptionByUserId(userId);
    if (!subscription) {
      return res.status(404).json({ error: 'No subscription found' });
    }

    if (!subscription.cancelAtPeriodEnd) {
      return res.status(400).json({ error: 'Subscription is not set to cancel' });
    }

    // Reactivate subscription in Stripe
    await stripeClient.subscriptions.update(subscription.stripeSubscriptionId, {
      cancel_at_period_end: false,
    });

    // Update subscription in database
    await cancelSubscription(subscription.stripeSubscriptionId);

    res.status(200).json({ 
      message: 'Subscription reactivated successfully',
      cancelAtPeriodEnd: false,
    });
  } catch (error: any) {
    console.error('Error reactivating subscription:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get available subscription plans
app.get('/plans', async (req: Request, res: Response) => {
  try {
    const plans = [
      { credits: 10000, amount: 2000, savings: 1000 }, // $20 vs $30
      { credits: 20000, amount: 4000, savings: 2000 }, // $40 vs $60
      { credits: 30000, amount: 6000, savings: 3000 }, // $60 vs $90
      { credits: 40000, amount: 8000, savings: 4000 }, // $80 vs $120
      { credits: 50000, amount: 10000, savings: 5000 }, // $100 vs $150
    ];

    res.status(200).json({ plans });
  } catch (error: any) {
    console.error('Error getting subscription plans:', error);
    res.status(500).json({ error: error.message });
  }
});

// Admin: Get subscription statistics
app.get('/admin/stats', async (req: Request, res: Response) => {
  try {
    // Note: Add admin authentication middleware here
    const stats = await getSubscriptionStats();
    res.status(200).json(stats);
  } catch (error: any) {
    console.error('Error getting subscription stats:', error);
    res.status(500).json({ error: error.message });
  }
});

export default app;
