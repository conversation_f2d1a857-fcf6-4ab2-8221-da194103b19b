import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { stripeClient } from '../payments/stripe';

dotenv.config();

const prisma = new PrismaClient();

interface ProductConfig {
  name: string;
  description: string;
  prices: {
    credits: number;
    amount: number; // in cents
    interval?: 'month' | null; // null for one-time payments
  }[];
}

const PRODUCTS_CONFIG: ProductConfig[] = [
  {
    name: 'SearchLeads Subscription Plan',
    description: 'Monthly subscription with discounted credit pricing',
    prices: [
      { credits: 10000, amount: 2000, interval: 'month' }, // $20 for 10K credits
      { credits: 20000, amount: 4000, interval: 'month' }, // $40 for 20K credits
      { credits: 30000, amount: 6000, interval: 'month' }, // $60 for 30K credits
      { credits: 40000, amount: 8000, interval: 'month' }, // $80 for 40K credits
      { credits: 50000, amount: 10000, interval: 'month' }, // $100 for 50K credits
    ],
  },
  {
    name: 'SearchLeads Pay-as-you-go',
    description: 'One-time credit purchases at standard pricing',
    prices: [
      { credits: 10000, amount: 3000, interval: null }, // $30 for 10K credits (existing pricing)
    ],
  },
];

export async function setupStripeProducts(): Promise<void> {
  console.log('Setting up Stripe products and prices...');

  try {
    for (const productConfig of PRODUCTS_CONFIG) {
      console.log(`Creating product: ${productConfig.name}`);

      // Create product in Stripe
      const stripeProduct = await stripeClient.products.create({
        name: productConfig.name,
        description: productConfig.description,
        active: true,
      });

      console.log(`Created Stripe product: ${stripeProduct.id}`);

      // Save product to database
      await prisma.stripeProduct.create({
        data: {
          id: stripeProduct.id,
          name: stripeProduct.name,
          description: stripeProduct.description || '',
          active: stripeProduct.active,
        },
      });

      console.log(`Saved product to database: ${stripeProduct.id}`);

      // Create prices for this product
      for (const priceConfig of productConfig.prices) {
        console.log(`Creating price for ${priceConfig.credits} credits...`);

        const stripePriceData: any = {
          product: stripeProduct.id,
          unit_amount: priceConfig.amount,
          currency: 'usd',
          active: true,
        };

        // Add recurring data for subscription prices
        if (priceConfig.interval) {
          stripePriceData.recurring = {
            interval: priceConfig.interval,
            interval_count: 1,
          };
        }

        const stripePrice = await stripeClient.prices.create(stripePriceData);

        console.log(`Created Stripe price: ${stripePrice.id}`);

        // Save price to database
        await prisma.stripePrice.create({
          data: {
            id: stripePrice.id,
            productId: stripeProduct.id,
            unitAmount: priceConfig.amount,
            currency: 'usd',
            interval: priceConfig.interval,
            intervalCount: priceConfig.interval ? 1 : null,
            credits: priceConfig.credits,
            active: true,
          },
        });

        console.log(`Saved price to database: ${stripePrice.id}`);

        // Update environment variables with price IDs
        await updateEnvVariable(getPriceEnvKey(productConfig.name, priceConfig), stripePrice.id);
      }

      // Update environment variables with product IDs
      await updateEnvVariable(getProductEnvKey(productConfig.name), stripeProduct.id);
    }

    console.log('Stripe setup completed successfully!');
  } catch (error) {
    console.error('Error setting up Stripe products:', error);
    throw error;
  }
}

function getPriceEnvKey(productName: string, priceConfig: any): string {
  const isSubscription = productName.includes('Subscription');
  const credits = priceConfig.credits;

  if (isSubscription) {
    return `STRIPE_SUBSCRIPTION_${credits / 1000}K_PRICE_ID`;
  } else {
    return `STRIPE_PAYG_${credits / 1000}K_PRICE_ID`;
  }
}

function getProductEnvKey(productName: string): string {
  const isSubscription = productName.includes('Subscription');
  return isSubscription ? 'STRIPE_SUBSCRIPTION_PRODUCT_ID' : 'STRIPE_PAYG_PRODUCT_ID';
}

async function updateEnvVariable(key: string, value: string): Promise<void> {
  const envFilePath = path.resolve(__dirname, '../.env');
  
  if (!fs.existsSync(envFilePath)) {
    throw new Error('.env file not found');
  }

  let envFileContent = fs.readFileSync(envFilePath, 'utf8');
  const regex = new RegExp(`^${key}=.*$`, 'm');
  
  if (regex.test(envFileContent)) {
    envFileContent = envFileContent.replace(regex, `${key}="${value}"`);
  } else {
    envFileContent += `\n${key}="${value}"`;
  }
  
  fs.writeFileSync(envFilePath, envFileContent);
  console.log(`Updated ${key} in .env file`);
}

export async function getStripeProductByType(isSubscription: boolean): Promise<string | null> {
  try {
    const product = await prisma.stripeProduct.findFirst({
      where: {
        name: {
          contains: isSubscription ? 'Subscription' : 'Pay-as-you-go',
        },
        active: true,
      },
    });
    return product?.id || null;
  } catch (error) {
    console.error('Error getting Stripe product:', error);
    return null;
  }
}

export async function getStripePriceByCredits(credits: number, isSubscription: boolean): Promise<string | null> {
  try {
    const price = await prisma.stripePrice.findFirst({
      where: {
        credits: credits,
        interval: isSubscription ? 'month' : null,
        active: true,
      },
      include: {
        product: true,
      },
    });
    return price?.id || null;
  } catch (error) {
    console.error('Error getting Stripe price:', error);
    return null;
  }
}

// CLI script to run setup
if (require.main === module) {
  setupStripeProducts()
    .then(() => {
      console.log('Setup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Setup failed:', error);
      process.exit(1);
    });
}
