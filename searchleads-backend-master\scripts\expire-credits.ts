#!/usr/bin/env ts-node

/**
 * Credit Expiration Script
 * 
 * This script manually runs the credit expiration job and provides detailed reporting.
 * 
 * Usage:
 * npm run expire-credits
 * npm run expire-credits -- --dry-run
 * npm run expire-credits -- --user-id USER_ID
 */

import dotenv from 'dotenv';
import { runCreditExpirationJob, sendExpirationWarnings, getCreditExpirationStats } from '../jobs/creditExpiration';
import { expireCredits } from '../db/creditLedger';

dotenv.config();

interface ScriptOptions {
  dryRun: boolean;
  userId?: string;
  warnings: boolean;
  stats: boolean;
}

function parseArgs(): ScriptOptions {
  const args = process.argv.slice(2);
  
  return {
    dryRun: args.includes('--dry-run'),
    userId: args.find(arg => arg.startsWith('--user-id='))?.split('=')[1],
    warnings: args.includes('--warnings'),
    stats: args.includes('--stats'),
  };
}

async function runExpirationForUser(userId: string, dryRun: boolean): Promise<void> {
  console.log(`🔍 Processing credit expiration for user: ${userId}`);
  
  if (dryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
    // TODO: Implement dry run logic to show what would be expired
    return;
  }

  try {
    const result = await expireCredits(userId);
    
    if (result.totalExpiredCredits > 0) {
      console.log(`✅ Expired ${result.totalExpiredCredits} credits in ${result.expiredTransactions} transactions`);
    } else {
      console.log('ℹ️  No expired credits found for this user');
    }
  } catch (error: any) {
    console.error(`❌ Error expiring credits for user ${userId}:`, error.message);
    throw error;
  }
}

async function runFullExpirationJob(dryRun: boolean): Promise<void> {
  console.log('🚀 Running full credit expiration job...');
  
  if (dryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
    // TODO: Implement dry run logic
    return;
  }

  try {
    const result = await runCreditExpirationJob();
    
    console.log('\n📊 EXPIRATION JOB RESULTS:');
    console.log(`   • Users processed: ${result.totalUsersProcessed}`);
    console.log(`   • Credits expired: ${result.totalCreditsExpired}`);
    console.log(`   • Transactions expired: ${result.totalTransactionsExpired}`);
    console.log(`   • Execution time: ${result.executionTime}ms`);
    
    if (result.errors.length > 0) {
      console.log(`   • Errors: ${result.errors.length}`);
      result.errors.forEach(error => console.log(`     - ${error}`));
    }
  } catch (error: any) {
    console.error('❌ Credit expiration job failed:', error.message);
    throw error;
  }
}

async function sendWarnings(): Promise<void> {
  console.log('📧 Sending expiration warnings...');
  
  try {
    const result = await sendExpirationWarnings(7); // 7 days warning
    
    console.log('\n📧 WARNING RESULTS:');
    console.log(`   • Users notified: ${result.usersNotified}`);
    
    if (result.errors.length > 0) {
      console.log(`   • Errors: ${result.errors.length}`);
      result.errors.forEach(error => console.log(`     - ${error}`));
    }
  } catch (error: any) {
    console.error('❌ Failed to send expiration warnings:', error.message);
    throw error;
  }
}

async function showStats(): Promise<void> {
  console.log('📊 Getting credit expiration statistics...');
  
  try {
    const stats = await getCreditExpirationStats();
    
    console.log('\n📈 CREDIT EXPIRATION STATISTICS:');
    console.log(`   • Total expired credits (last 30 days): ${stats.totalExpiredCredits}`);
    console.log(`   • Total expiring credits (next 30 days): ${stats.totalExpiringCredits}`);
    console.log(`   • Users with expiring credits: ${stats.usersWithExpiringCredits}`);
    console.log(`   • Next expiration date: ${stats.nextExpirationDate ? stats.nextExpirationDate.toDateString() : 'None'}`);
  } catch (error: any) {
    console.error('❌ Failed to get expiration statistics:', error.message);
    throw error;
  }
}

async function main(): Promise<void> {
  console.log('🕒 SearchLeads Credit Expiration Tool\n');

  // Validate environment
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is required');
    process.exit(1);
  }

  const options = parseArgs();

  try {
    // Show statistics if requested
    if (options.stats) {
      await showStats();
      console.log('');
    }

    // Send warnings if requested
    if (options.warnings) {
      await sendWarnings();
      console.log('');
    }

    // Run expiration for specific user
    if (options.userId) {
      await runExpirationForUser(options.userId, options.dryRun);
    } else {
      // Run full expiration job
      await runFullExpirationJob(options.dryRun);
    }

    console.log('\n✅ Credit expiration process completed successfully');
    
  } catch (error: any) {
    console.error('\n❌ Credit expiration process failed:', error.message);
    process.exit(1);
  }
}

// Show help if requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
SearchLeads Credit Expiration Tool

Usage:
  npm run expire-credits                    # Run full expiration job
  npm run expire-credits -- --dry-run      # Show what would be expired (no changes)
  npm run expire-credits -- --user-id=ID   # Expire credits for specific user
  npm run expire-credits -- --warnings     # Send expiration warnings
  npm run expire-credits -- --stats        # Show expiration statistics

Options:
  --dry-run              Show what would be expired without making changes
  --user-id=USER_ID      Process expiration for specific user only
  --warnings             Send expiration warnings to users
  --stats                Show credit expiration statistics
  --help, -h             Show this help message

Examples:
  npm run expire-credits -- --stats --warnings
  npm run expire-credits -- --user-id=user123 --dry-run
  npm run expire-credits -- --dry-run --stats
`);
  process.exit(0);
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

export { main as expireCreditsScript };
