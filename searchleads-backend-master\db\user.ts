import { PrismaClient, User } from '@prisma/client';
import { v4 } from 'uuid';
import { addPaygCredits, useCredits, getUserCredits } from './creditManager';

const prisma = new PrismaClient();

export async function createUser(
    fullName: string,
    companyName: string,
    phoneNumber: string,
    location: string,
    userID: string,
    email: string,
    credits: number,
    heardFrom: string
): Promise<User | null> {
    try {
        const user = await prisma.user.create({
            data: {
                UserID: userID,
                email: email,
                name: fullName,
                companyName: companyName,
                phoneNumber: phoneNumber,
                location: location,
                credits: credits,
                apikey: v4(),
                heardFrom: heardFrom,
            },
        });

        return user;
    } catch (error: any) {
        throw new Error(error.message);
    }
}

export async function getUser(userID: string): Promise<User | null> {
    try {
        const user = await prisma.user.findUnique({
            where: {
                UserID: userID,
            },
        });
        return user || null;
    } catch (error: any) {
        throw new Error(error.message);
    }
}

export async function addCredits(addCreds: number, userId: string, paymentIntentId?: string): Promise<User | null> {
    try {
        // Use new credit manager for PAYG credits with payment intent
        if (paymentIntentId) {
            const result = await addPaygCredits(userId, addCreds, paymentIntentId);
            if (result.success) {
                return await getUser(userId);
            } else {
                throw new Error(result.error || 'Failed to add credits');
            }
        }

        // Legacy fallback for backward compatibility
        let data = await prisma.user.findUnique({
            where: {
                UserID: userId,
            },
        });

        if (!data) {
            return null;
        }

        data = await prisma.user.update({
            where: {
                UserID: userId,
            },
            data: {
                credits: {
                    increment: Math.abs(addCreds),
                },
                TotalCreditsBought: {
                    increment: Math.abs(addCreds),
                },
            },
        });

        return data;
    } catch (error: any) {
        throw new Error(error.message);
    }
}

export async function refreshAPIKey(userID: string): Promise<User | null> {
    try {
        const user = await prisma.user.update({
            where: {
                UserID: userID,
            },
            data: {
                apikey: v4(),
            },
        });

        return user;
    } catch (error: any) {
        throw new Error(error.message);
    }
}


export async function removeCredits(removeCreds: number, userId: string, description?: string, sourceId?: string): Promise<User | null> {
    try {
        // Use new credit manager for proper FIFO credit usage
        const result = await useCredits(
            userId,
            removeCreds,
            description || 'Credit usage',
            sourceId
        );

        if (result.success) {
            return await getUser(userId);
        } else {
            throw new Error(result.error || 'Insufficient credits');
        }
    } catch (error: any) {
        throw new Error(error.message);
    }
}

export async function getCredits(userID: string): Promise<number | null> {
    try {
        const data = await prisma.user.findUnique({
            where: {
                UserID: userID,
            },
        });

        return data ? data.credits : null;
    } catch (error: any) {
        throw new Error(error.message);
    }
}
