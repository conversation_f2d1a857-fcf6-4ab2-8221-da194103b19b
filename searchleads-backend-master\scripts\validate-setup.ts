#!/usr/bin/env ts-node

/**
 * Setup Validation Script
 * 
 * This script validates that all components of the SearchLeads subscription system
 * are properly configured and working.
 */

import dotenv from 'dotenv';
import { PrismaClient } from '@prisma/client';
import { stripeClient } from '../payments/stripe';
import { validateStripeConfig, getAvailableSubscriptionPlans, getAvailablePaygOptions } from '../utils/stripeConfig';

dotenv.config();

const prisma = new PrismaClient();

async function validateDatabase() {
  console.log('🔍 Validating database connection...');
  
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Check if subscription tables exist
    const productCount = await prisma.stripeProduct.count();
    const priceCount = await prisma.stripePrice.count();
    const subscriptionCount = await prisma.subscription.count();
    
    console.log(`✅ Database tables exist:`);
    console.log(`   • StripeProduct: ${productCount} records`);
    console.log(`   • StripePrice: ${priceCount} records`);
    console.log(`   • Subscription: ${subscriptionCount} records`);
    
    return true;
  } catch (error) {
    console.error('❌ Database validation failed:', error);
    return false;
  }
}

async function validateStripe() {
  console.log('\n🔍 Validating Stripe configuration...');
  
  try {
    // Test Stripe connection
    const account = await stripeClient.accounts.retrieve();
    console.log(`✅ Stripe connection successful (Account: ${account.id})`);
    
    // Validate configuration
    const configValidation = await validateStripeConfig();
    
    if (configValidation.isValid) {
      console.log('✅ Stripe configuration is valid');
    } else {
      console.log('❌ Stripe configuration issues:');
      configValidation.errors.forEach(error => {
        console.log(`   • ${error}`);
      });
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ Stripe validation failed:', error);
    return false;
  }
}

async function validateProducts() {
  console.log('\n🔍 Validating products and prices...');
  
  try {
    const subscriptionPlans = await getAvailableSubscriptionPlans();
    const paygOptions = await getAvailablePaygOptions();
    
    console.log('✅ Subscription plans:');
    subscriptionPlans.forEach(plan => {
      console.log(`   • ${plan.credits / 1000}K credits: $${plan.amount / 100}/month (Save $${plan.savings / 100})`);
    });
    
    console.log('✅ Pay-as-you-go options:');
    paygOptions.forEach(option => {
      console.log(`   • ${option.credits / 1000}K credits: $${option.amount / 100} one-time`);
    });
    
    return true;
  } catch (error) {
    console.error('❌ Product validation failed:', error);
    return false;
  }
}

async function validateEnvironmentVariables() {
  console.log('\n🔍 Validating environment variables...');
  
  const requiredVars = [
    'DATABASE_URL',
    'DIRECT_URL',
    'SUPABASE_BASE_URL',
    'SUPABASE_ANON_KEY',
    'STRIPE_PUBLIC_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'STRIPE_SUBSCRIPTION_PRODUCT_ID',
    'STRIPE_PAYG_PRODUCT_ID',
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length === 0) {
    console.log('✅ All required environment variables are set');
    return true;
  } else {
    console.log('❌ Missing environment variables:');
    missingVars.forEach(varName => {
      console.log(`   • ${varName}`);
    });
    return false;
  }
}

async function testStripeWebhook() {
  console.log('\n🔍 Testing webhook configuration...');
  
  try {
    // List webhook endpoints
    const webhooks = await stripeClient.webhookEndpoints.list();
    
    if (webhooks.data.length === 0) {
      console.log('⚠️  No webhook endpoints configured in Stripe');
      console.log('   Please configure webhook endpoint in Stripe Dashboard');
      return false;
    }
    
    console.log('✅ Webhook endpoints found:');
    webhooks.data.forEach(webhook => {
      console.log(`   • ${webhook.url} (${webhook.status})`);
      console.log(`     Events: ${webhook.enabled_events.join(', ')}`);
    });
    
    return true;
  } catch (error) {
    console.error('❌ Webhook validation failed:', error);
    return false;
  }
}

async function generateTestReport() {
  console.log('\n📊 Generating test report...');
  
  try {
    // Get products from Stripe
    const stripeProducts = await stripeClient.products.list({ active: true });
    const stripePrices = await stripeClient.prices.list({ active: true });
    
    // Get products from database
    const dbProducts = await prisma.stripeProduct.findMany({
      include: { prices: true }
    });
    
    console.log('\n📋 Configuration Summary:');
    console.log(`   • Stripe Products: ${stripeProducts.data.length}`);
    console.log(`   • Stripe Prices: ${stripePrices.data.length}`);
    console.log(`   • Database Products: ${dbProducts.length}`);
    console.log(`   • Database Prices: ${dbProducts.reduce((sum, p) => sum + p.prices.length, 0)}`);
    
    console.log('\n💰 Pricing Structure:');
    console.log('   Subscription (Monthly):');
    console.log('   • 10K credits: $20/month (33% savings vs PAYG)');
    console.log('   • 20K credits: $40/month (33% savings vs PAYG)');
    console.log('   • 30K credits: $60/month (33% savings vs PAYG)');
    console.log('   • 40K credits: $80/month (33% savings vs PAYG)');
    console.log('   • 50K credits: $100/month (33% savings vs PAYG)');
    console.log('   Pay-as-you-go:');
    console.log('   • 10K credits: $30 one-time');
    
    return true;
  } catch (error) {
    console.error('❌ Report generation failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting SearchLeads Subscription System Validation\n');
  
  const results = {
    database: await validateDatabase(),
    environment: await validateEnvironmentVariables(),
    stripe: await validateStripe(),
    products: await validateProducts(),
    webhooks: await testStripeWebhook(),
    report: await generateTestReport(),
  };
  
  const allPassed = Object.values(results).every(result => result === true);
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 VALIDATION SUMMARY');
  console.log('='.repeat(60));
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.toUpperCase()}`);
  });
  
  if (allPassed) {
    console.log('\n🎉 All validations passed! Your subscription system is ready.');
    console.log('\n📝 Next steps:');
    console.log('   1. Deploy your application');
    console.log('   2. Test subscription creation with a real payment method');
    console.log('   3. Monitor webhook events in Stripe Dashboard');
    console.log('   4. Test subscription cancellation and reactivation');
  } else {
    console.log('\n⚠️  Some validations failed. Please fix the issues above.');
  }
  
  await prisma.$disconnect();
  process.exit(allPassed ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    console.error('Validation script failed:', error);
    process.exit(1);
  });
}
