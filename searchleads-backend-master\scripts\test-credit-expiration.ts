#!/usr/bin/env ts-node

/**
 * Credit Expiration System Test Script
 * 
 * This script tests the complete credit expiration flow to ensure it works correctly.
 * 
 * Usage:
 * npm run test-credit-expiration
 */

import dotenv from 'dotenv';
import { PrismaClient, CreditTransactionType } from '@prisma/client';
import { 
  addSubscriptionCredits, 
  addPaygCredits, 
  addBonusCredits, 
  useCredits,
  getUserCredits 
} from '../db/creditManager';
import { expireCredits } from '../db/creditLedger';
import { runCreditExpirationJob } from '../jobs/creditExpiration';

dotenv.config();

const prisma = new PrismaClient();

interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

class CreditExpirationTester {
  private testUserId: string;
  private results: TestResult[] = [];

  constructor() {
    this.testUserId = `test_user_${Date.now()}`;
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Credit Expiration System Tests\n');

    try {
      await this.setupTestUser();
      
      await this.testSubscriptionCreditsExpiration();
      await this.testPaygCreditsNoExpiration();
      await this.testBonusCreditsExpiration();
      await this.testFIFOCreditUsage();
      await this.testExpirationJob();
      await this.testCreditBalance();
      
      await this.cleanupTestUser();
      
      this.printResults();
    } catch (error: any) {
      console.error('❌ Test suite failed:', error.message);
      await this.cleanupTestUser();
      process.exit(1);
    }
  }

  private async setupTestUser(): Promise<void> {
    console.log('🔧 Setting up test user...');
    
    try {
      await prisma.user.create({
        data: {
          UserID: this.testUserId,
          name: 'Test User',
          email: '<EMAIL>',
          credits: 0,
          TotalCreditsBought: 0,
          TotalCreditsUsed: 0,
        },
      });
      
      console.log(`✅ Test user created: ${this.testUserId}\n`);
    } catch (error: any) {
      throw new Error(`Failed to create test user: ${error.message}`);
    }
  }

  private async cleanupTestUser(): Promise<void> {
    console.log('\n🧹 Cleaning up test user...');
    
    try {
      // Delete credit transactions first (foreign key constraint)
      await prisma.creditTransaction.deleteMany({
        where: { userId: this.testUserId },
      });
      
      // Delete user
      await prisma.user.delete({
        where: { UserID: this.testUserId },
      });
      
      console.log('✅ Test user cleaned up');
    } catch (error: any) {
      console.error('⚠️  Failed to cleanup test user:', error.message);
    }
  }

  private async testSubscriptionCreditsExpiration(): Promise<void> {
    const testName = 'Subscription Credits Expiration (30 days)';
    console.log(`🧪 Testing: ${testName}`);

    try {
      // Add subscription credits (should expire in 30 days)
      const result = await addSubscriptionCredits(
        this.testUserId,
        1000,
        'test_subscription_123',
        'Test subscription credits'
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to add subscription credits');
      }

      // Check that credits were added with expiration
      const balance = await getUserCredits(this.testUserId);
      const hasExpiringCredits = balance.balance.expiringCredits.length > 0;
      
      this.results.push({
        testName,
        passed: hasExpiringCredits && balance.balance.availableCredits === 1000,
        details: {
          availableCredits: balance.balance.availableCredits,
          expiringCredits: balance.balance.expiringCredits.length,
        },
      });

      console.log(`   ✅ Added 1000 subscription credits with expiration`);
    } catch (error: any) {
      this.results.push({
        testName,
        passed: false,
        error: error.message,
      });
      console.log(`   ❌ ${error.message}`);
    }
  }

  private async testPaygCreditsNoExpiration(): Promise<void> {
    const testName = 'Pay-as-you-go Credits (No Expiration)';
    console.log(`🧪 Testing: ${testName}`);

    try {
      // Add PAYG credits (should not expire)
      const result = await addPaygCredits(
        this.testUserId,
        500,
        'test_payment_intent_123',
        'Test PAYG credits'
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to add PAYG credits');
      }

      // Check that credits were added without expiration
      const balance = await getUserCredits(this.testUserId);
      
      this.results.push({
        testName,
        passed: balance.balance.availableCredits === 1500, // 1000 + 500
        details: {
          availableCredits: balance.balance.availableCredits,
          totalCredits: balance.balance.totalCredits,
        },
      });

      console.log(`   ✅ Added 500 PAYG credits without expiration`);
    } catch (error: any) {
      this.results.push({
        testName,
        passed: false,
        error: error.message,
      });
      console.log(`   ❌ ${error.message}`);
    }
  }

  private async testBonusCreditsExpiration(): Promise<void> {
    const testName = 'Bonus Credits Expiration (Custom Days)';
    console.log(`🧪 Testing: ${testName}`);

    try {
      // Add bonus credits with 1-day expiration for testing
      const result = await addBonusCredits(
        this.testUserId,
        200,
        'Test bonus credits',
        1 // 1 day expiration
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to add bonus credits');
      }

      const balance = await getUserCredits(this.testUserId);
      
      this.results.push({
        testName,
        passed: balance.balance.availableCredits === 1700, // 1500 + 200
        details: {
          availableCredits: balance.balance.availableCredits,
          expiringCredits: balance.balance.expiringCredits.length,
        },
      });

      console.log(`   ✅ Added 200 bonus credits with 1-day expiration`);
    } catch (error: any) {
      this.results.push({
        testName,
        passed: false,
        error: error.message,
      });
      console.log(`   ❌ ${error.message}`);
    }
  }

  private async testFIFOCreditUsage(): Promise<void> {
    const testName = 'FIFO Credit Usage (Oldest First)';
    console.log(`🧪 Testing: ${testName}`);

    try {
      // Use some credits (should use oldest first)
      const result = await useCredits(
        this.testUserId,
        300,
        'Test credit usage',
        'test_usage_123'
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to use credits');
      }

      const balance = await getUserCredits(this.testUserId);
      
      this.results.push({
        testName,
        passed: result.creditsUsed === 300 && balance.balance.availableCredits === 1400,
        details: {
          creditsUsed: result.creditsUsed,
          remainingCredits: balance.balance.availableCredits,
        },
      });

      console.log(`   ✅ Used 300 credits using FIFO logic`);
    } catch (error: any) {
      this.results.push({
        testName,
        passed: false,
        error: error.message,
      });
      console.log(`   ❌ ${error.message}`);
    }
  }

  private async testExpirationJob(): Promise<void> {
    const testName = 'Credit Expiration Job';
    console.log(`🧪 Testing: ${testName}`);

    try {
      // Manually expire credits for this user to test the job
      const expirationResult = await expireCredits(this.testUserId);
      
      // Since we set bonus credits to expire in 1 day, and they're in the past for testing,
      // we need to manually set an expired date
      await prisma.creditTransaction.updateMany({
        where: {
          userId: this.testUserId,
          type: CreditTransactionType.BONUS,
        },
        data: {
          expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        },
      });

      // Run expiration again
      const secondExpirationResult = await expireCredits(this.testUserId);
      
      this.results.push({
        testName,
        passed: secondExpirationResult.totalExpiredCredits > 0,
        details: {
          expiredCredits: secondExpirationResult.totalExpiredCredits,
          expiredTransactions: secondExpirationResult.expiredTransactions,
        },
      });

      console.log(`   ✅ Expired ${secondExpirationResult.totalExpiredCredits} credits`);
    } catch (error: any) {
      this.results.push({
        testName,
        passed: false,
        error: error.message,
      });
      console.log(`   ❌ ${error.message}`);
    }
  }

  private async testCreditBalance(): Promise<void> {
    const testName = 'Credit Balance Calculation';
    console.log(`🧪 Testing: ${testName}`);

    try {
      const balance = await getUserCredits(this.testUserId);
      const user = balance.user;
      
      // Check that legacy credits field matches available credits
      const balanceMatches = Math.abs((user?.credits || 0) - balance.balance.availableCredits) < 0.01;
      
      this.results.push({
        testName,
        passed: balanceMatches,
        details: {
          legacyCredits: user?.credits,
          availableCredits: balance.balance.availableCredits,
          totalCredits: balance.balance.totalCredits,
          expiredCredits: balance.balance.expiredCredits,
        },
      });

      console.log(`   ✅ Credit balance calculation verified`);
    } catch (error: any) {
      this.results.push({
        testName,
        passed: false,
        error: error.message,
      });
      console.log(`   ❌ ${error.message}`);
    }
  }

  private printResults(): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 CREDIT EXPIRATION TEST RESULTS');
    console.log('='.repeat(60));

    const passedTests = this.results.filter(r => r.passed).length;
    const totalTests = this.results.length;

    this.results.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.testName}`);
      
      if (result.error) {
        console.log(`     Error: ${result.error}`);
      }
      
      if (result.details) {
        console.log(`     Details: ${JSON.stringify(result.details, null, 2)}`);
      }
    });

    console.log('\n' + '='.repeat(60));
    console.log(`📈 SUMMARY: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! Credit expiration system is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the results above.');
      process.exit(1);
    }
  }
}

async function main(): Promise<void> {
  // Validate environment
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is required');
    process.exit(1);
  }

  const tester = new CreditExpirationTester();
  await tester.runAllTests();
  
  await prisma.$disconnect();
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('Test script failed:', error);
    process.exit(1);
  });
}

export { main as testCreditExpirationScript };
