import { PrismaClient, CreditTransactionType, User } from '@prisma/client';
import { addCreditsToLedger, useCreditsFromLedger, getUserCreditBalance, expireCredits } from './creditLedger';

const prisma = new PrismaClient();

/**
 * Enhanced Credit Manager
 * 
 * This module provides high-level credit management functions that use the
 * new ledger system while maintaining backward compatibility.
 */

/**
 * Add subscription credits with 30-day expiration
 */
export async function addSubscriptionCredits(
  userId: string,
  amount: number,
  subscriptionId: string,
  description?: string
): Promise<{ success: boolean; transaction?: any; error?: string }> {
  try {
    // Subscription credits expire after 30 days
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    const transaction = await addCreditsToLedger({
      userId,
      amount,
      type: CreditTransactionType.PURCHASE_SUBSCRIPTION,
      source: 'subscription',
      sourceId: subscriptionId,
      description: description || `Subscription credits - ${amount} credits`,
      expiresAt,
    });

    // Update user's total credits bought
    await prisma.user.update({
      where: { UserID: userId },
      data: {
        TotalCreditsBought: { increment: amount },
      },
    });

    return { success: true, transaction };
  } catch (error: any) {
    console.error('Error adding subscription credits:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Add pay-as-you-go credits (no expiration)
 */
export async function addPaygCredits(
  userId: string,
  amount: number,
  paymentIntentId: string,
  description?: string
): Promise<{ success: boolean; transaction?: any; error?: string }> {
  try {
    const transaction = await addCreditsToLedger({
      userId,
      amount,
      type: CreditTransactionType.PURCHASE_PAYG,
      source: 'payg',
      sourceId: paymentIntentId,
      description: description || `Pay-as-you-go credits - ${amount} credits`,
      // No expiration for PAYG credits
    });

    // Update user's total credits bought
    await prisma.user.update({
      where: { UserID: userId },
      data: {
        TotalCreditsBought: { increment: amount },
      },
    });

    return { success: true, transaction };
  } catch (error: any) {
    console.error('Error adding PAYG credits:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Add admin credits (configurable expiration)
 */
export async function addAdminCredits(
  userId: string,
  amount: number,
  description: string,
  expirationDays?: number
): Promise<{ success: boolean; transaction?: any; error?: string }> {
  try {
    let expiresAt: Date | undefined;
    
    if (expirationDays && expirationDays > 0) {
      expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expirationDays);
    }

    const transaction = await addCreditsToLedger({
      userId,
      amount,
      type: CreditTransactionType.ADMIN_ADJUSTMENT,
      source: 'admin',
      description,
      expiresAt,
    });

    // Update user's total credits bought if positive adjustment
    if (amount > 0) {
      await prisma.user.update({
        where: { UserID: userId },
        data: {
          TotalCreditsBought: { increment: amount },
        },
      });
    }

    return { success: true, transaction };
  } catch (error: any) {
    console.error('Error adding admin credits:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Add bonus credits with expiration
 */
export async function addBonusCredits(
  userId: string,
  amount: number,
  description: string,
  expirationDays: number = 90
): Promise<{ success: boolean; transaction?: any; error?: string }> {
  try {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expirationDays);

    const transaction = await addCreditsToLedger({
      userId,
      amount,
      type: CreditTransactionType.BONUS,
      source: 'bonus',
      description,
      expiresAt,
    });

    return { success: true, transaction };
  } catch (error: any) {
    console.error('Error adding bonus credits:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Use credits for lead searches or other services
 */
export async function useCredits(
  userId: string,
  amount: number,
  description: string,
  sourceId?: string
): Promise<{ success: boolean; creditsUsed?: number; error?: string }> {
  try {
    // First, expire any expired credits
    await expireCredits(userId);

    const result = await useCreditsFromLedger({
      userId,
      amount,
      description,
      sourceId,
    });

    if (result.success) {
      // Update user's total credits used
      await prisma.user.update({
        where: { UserID: userId },
        data: {
          TotalCreditsUsed: { increment: result.creditsUsed },
        },
      });

      return { success: true, creditsUsed: result.creditsUsed };
    } else {
      return { success: false, error: 'Insufficient credits' };
    }
  } catch (error: any) {
    console.error('Error using credits:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get user's detailed credit information
 */
export async function getUserCredits(userId: string): Promise<{
  balance: any;
  user: User | null;
  error?: string;
}> {
  try {
    // Expire any expired credits first
    await expireCredits(userId);

    const balance = await getUserCreditBalance(userId);
    const user = await prisma.user.findUnique({
      where: { UserID: userId },
    });

    return { balance, user };
  } catch (error: any) {
    console.error('Error getting user credits:', error);
    return { balance: null, user: null, error: error.message };
  }
}

/**
 * Check if user has sufficient credits
 */
export async function hasEnoughCredits(userId: string, requiredAmount: number): Promise<boolean> {
  try {
    // Expire any expired credits first
    await expireCredits(userId);

    const balance = await getUserCreditBalance(userId);
    return balance.availableCredits >= requiredAmount;
  } catch (error: any) {
    console.error('Error checking credit sufficiency:', error);
    return false;
  }
}

/**
 * Get credits expiring soon (within specified days)
 */
export async function getExpiringCredits(userId: string, withinDays: number = 7): Promise<{
  expiringCredits: Array<{ amount: number; expiresAt: Date }>;
  totalExpiring: number;
}> {
  try {
    const balance = await getUserCreditBalance(userId);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() + withinDays);

    const expiringCredits = balance.expiringCredits.filter(
      credit => credit.expiresAt <= cutoffDate
    );

    const totalExpiring = expiringCredits.reduce((sum, credit) => sum + credit.amount, 0);

    return { expiringCredits, totalExpiring };
  } catch (error: any) {
    console.error('Error getting expiring credits:', error);
    return { expiringCredits: [], totalExpiring: 0 };
  }
}

/**
 * Refund credits (e.g., for failed operations)
 */
export async function refundCredits(
  userId: string,
  amount: number,
  description: string,
  originalTransactionId?: string
): Promise<{ success: boolean; transaction?: any; error?: string }> {
  try {
    const transaction = await addCreditsToLedger({
      userId,
      amount,
      type: CreditTransactionType.REFUND,
      source: 'refund',
      sourceId: originalTransactionId,
      description,
      // Refunded credits don't expire
    });

    return { success: true, transaction };
  } catch (error: any) {
    console.error('Error refunding credits:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get credit usage statistics for a user
 */
export async function getCreditStats(userId: string): Promise<{
  totalPurchased: number;
  totalUsed: number;
  totalExpired: number;
  totalRefunded: number;
  currentBalance: number;
  error?: string;
}> {
  try {
    const transactions = await prisma.creditTransaction.findMany({
      where: { userId },
    });

    let totalPurchased = 0;
    let totalUsed = 0;
    let totalExpired = 0;
    let totalRefunded = 0;

    for (const transaction of transactions) {
      switch (transaction.type) {
        case CreditTransactionType.PURCHASE_SUBSCRIPTION:
        case CreditTransactionType.PURCHASE_PAYG:
        case CreditTransactionType.BONUS:
          totalPurchased += transaction.amount;
          break;
        case CreditTransactionType.USAGE:
          totalUsed += Math.abs(transaction.amount);
          break;
        case CreditTransactionType.EXPIRATION:
          totalExpired += Math.abs(transaction.amount);
          break;
        case CreditTransactionType.REFUND:
          totalRefunded += transaction.amount;
          break;
      }
    }

    const balance = await getUserCreditBalance(userId);

    return {
      totalPurchased,
      totalUsed,
      totalExpired,
      totalRefunded,
      currentBalance: balance.availableCredits,
    };
  } catch (error: any) {
    console.error('Error getting credit stats:', error);
    return {
      totalPurchased: 0,
      totalUsed: 0,
      totalExpired: 0,
      totalRefunded: 0,
      currentBalance: 0,
      error: error.message,
    };
  }
}
