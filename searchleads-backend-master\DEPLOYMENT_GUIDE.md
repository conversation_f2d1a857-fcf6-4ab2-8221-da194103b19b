# SearchLeads Subscription System - Deployment Guide

## 🎉 System Status: READY FOR PRODUCTION

Your SearchLeads subscription system has been successfully configured and validated. All components are working correctly and ready for production deployment.

## ✅ What's Been Completed

### 1. Database Schema
- ✅ Added subscription models to Prisma schema
- ✅ Created and applied database migrations
- ✅ Added `stripeCustomerId` to User model
- ✅ Created StripeProduct, StripePrice, and Subscription tables

### 2. Stripe Integration
- ✅ Created live Stripe products and prices
- ✅ Configured subscription pricing: $20 per 10K credits (monthly)
- ✅ Maintained existing pay-as-you-go: $30 per 10K credits
- ✅ Set up tiered pricing (10K, 20K, 30K, 40K, 50K credits)
- ✅ All Stripe IDs stored in database and environment variables

### 3. API Endpoints
- ✅ `/api/subscription/create` - Create new subscription
- ✅ `/api/subscription/current` - Get current subscription
- ✅ `/api/subscription/cancel` - Cancel subscription
- ✅ `/api/subscription/reactivate` - Reactivate subscription
- ✅ `/api/subscription/plans` - Get available plans
- ✅ Enhanced `/api/user/getUser` with subscription info

### 4. Webhook Handling
- ✅ Updated payment webhook to handle subscription events
- ✅ Automatic credit renewal on subscription billing
- ✅ Subscription status synchronization
- ✅ Failed payment handling

### 5. Configuration Management
- ✅ No hardcoded Stripe IDs - all loaded dynamically
- ✅ Environment variable validation
- ✅ Database-driven pricing configuration
- ✅ Caching for performance

## 🚀 Current Configuration

### Stripe Products Created
- **Subscription Product**: `prod_Sd3vkK3D4QUmuF`
- **Pay-as-you-go Product**: `prod_Sd3vjheBgG0NEM`

### Pricing Structure
**Subscription Plans (Monthly):**
- 10K credits: $20/month (33% savings)
- 20K credits: $40/month (33% savings)
- 30K credits: $60/month (33% savings)
- 40K credits: $80/month (33% savings)
- 50K credits: $100/month (33% savings)

**Pay-as-you-go (One-time):**
- 10K credits: $30 (existing pricing maintained)

### Webhook Configuration
Your webhook endpoint is configured at:
`https://api.searchleads.co/api/payments/searchLeadsConfirmPayment`

Events handled:
- `payment_intent.succeeded`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`

## 📋 Deployment Checklist

### Pre-Deployment
- [x] Database migrations applied
- [x] Stripe products and prices created
- [x] Environment variables configured
- [x] Webhook endpoints configured
- [x] All validations passed

### Deployment Steps
1. **Deploy Application**
   ```bash
   npm run build
   npm start
   ```

2. **Verify Deployment**
   ```bash
   npm run validate-setup
   ```

3. **Test Health Endpoint**
   ```bash
   curl https://yourdomain.com/health
   ```

### Post-Deployment Testing
1. **Test Subscription Creation**
   - Use Stripe test cards for initial testing
   - Verify credits are added to user account
   - Check subscription status in database

2. **Test Webhook Events**
   - Monitor Stripe Dashboard for webhook deliveries
   - Verify subscription renewals add credits
   - Test failed payment scenarios

3. **Test User Experience**
   - Verify subscription info appears in user profile
   - Test subscription cancellation
   - Test subscription reactivation

## 🔧 Maintenance Commands

### Validate System Health
```bash
npm run validate-setup
```

### Database Operations
```bash
npm run db:migrate    # Apply new migrations
npm run db:generate   # Regenerate Prisma client
```

### Stripe Operations
```bash
npm run setup-stripe  # Re-run Stripe setup (if needed)
```

## 📊 Monitoring

### Key Metrics to Monitor
1. **Subscription Metrics**
   - Active subscriptions count
   - Monthly recurring revenue (MRR)
   - Churn rate
   - Plan distribution

2. **Technical Metrics**
   - Webhook delivery success rate
   - API response times
   - Database performance
   - Error rates

### Stripe Dashboard
Monitor these sections in your Stripe Dashboard:
- Subscriptions overview
- Webhook delivery logs
- Payment failures
- Customer disputes

## 🚨 Troubleshooting

### Common Issues

1. **Webhook Signature Verification Fails**
   - Verify `STRIPE_WEBHOOK_SECRET` is correct
   - Check webhook endpoint URL
   - Ensure raw body is passed to webhook handler

2. **Credits Not Added on Renewal**
   - Check webhook event logs in Stripe
   - Verify subscription status in database
   - Check application logs for errors

3. **Subscription Creation Fails**
   - Verify payment method is valid
   - Check Stripe price IDs in database
   - Ensure customer creation is successful

### Support Contacts
- **Stripe Support**: https://support.stripe.com
- **Database Issues**: Check Supabase dashboard
- **Application Logs**: Monitor your deployment platform

## 🎯 Next Steps

1. **Go Live**: Your system is ready for production use
2. **Monitor**: Keep an eye on metrics and webhook deliveries
3. **Scale**: The system is designed to handle growth
4. **Optimize**: Consider adding features like:
   - Annual subscription discounts
   - Usage-based billing
   - Enterprise plans
   - Proration handling

## 📞 Emergency Procedures

### If Webhooks Stop Working
1. Check Stripe webhook endpoint status
2. Verify webhook secret hasn't changed
3. Check application deployment status
4. Review recent code changes

### If Subscriptions Fail
1. Check Stripe Dashboard for errors
2. Verify database connectivity
3. Check payment method validity
4. Review application logs

---

**🎉 Congratulations! Your SearchLeads subscription system is fully operational and ready for production use.**
