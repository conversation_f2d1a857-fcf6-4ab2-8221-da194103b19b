import { PrismaClient, Subscription, User } from '@prisma/client';

const prisma = new PrismaClient();

export async function createSubscription(
  userId: string,
  stripeSubscriptionId: string,
  stripePriceId: string,
  status: string,
  currentPeriodStart: Date,
  currentPeriodEnd: Date
): Promise<Subscription | null> {
  try {
    const subscription = await prisma.subscription.create({
      data: {
        id: generateSubscriptionId(),
        userId,
        stripeSubscriptionId,
        stripePriceId,
        status,
        currentPeriodStart,
        currentPeriodEnd,
        cancelAtPeriodEnd: false,
      },
    });

    return subscription;
  } catch (error: any) {
    console.error('Error creating subscription:', error);
    throw new Error(error.message);
  }
}

export async function updateSubscription(
  stripeSubscriptionId: string,
  updates: {
    status?: string;
    currentPeriodStart?: Date;
    currentPeriodEnd?: Date;
    cancelAtPeriodEnd?: boolean;
  }
): Promise<Subscription | null> {
  try {
    const subscription = await prisma.subscription.update({
      where: {
        stripeSubscriptionId,
      },
      data: {
        ...updates,
        updatedAt: new Date(),
      },
    });

    return subscription;
  } catch (error: any) {
    console.error('Error updating subscription:', error);
    throw new Error(error.message);
  }
}

export async function getSubscriptionByUserId(userId: string): Promise<Subscription | null> {
  try {
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId,
        status: {
          in: ['active', 'trialing', 'past_due'],
        },
      },
      include: {
        price: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return subscription;
  } catch (error: any) {
    console.error('Error getting subscription:', error);
    throw new Error(error.message);
  }
}

export async function getSubscriptionByStripeId(stripeSubscriptionId: string): Promise<Subscription | null> {
  try {
    const subscription = await prisma.subscription.findUnique({
      where: {
        stripeSubscriptionId,
      },
      include: {
        user: true,
        price: {
          include: {
            product: true,
          },
        },
      },
    });

    return subscription;
  } catch (error: any) {
    console.error('Error getting subscription by Stripe ID:', error);
    throw new Error(error.message);
  }
}

export async function cancelSubscription(stripeSubscriptionId: string): Promise<Subscription | null> {
  try {
    const subscription = await prisma.subscription.update({
      where: {
        stripeSubscriptionId,
      },
      data: {
        cancelAtPeriodEnd: true,
        updatedAt: new Date(),
      },
    });

    return subscription;
  } catch (error: any) {
    console.error('Error canceling subscription:', error);
    throw new Error(error.message);
  }
}

export async function addStripeCustomerId(userId: string, stripeCustomerId: string): Promise<User | null> {
  try {
    const user = await prisma.user.update({
      where: {
        UserID: userId,
      },
      data: {
        stripeCustomerId,
      },
    });

    return user;
  } catch (error: any) {
    console.error('Error adding Stripe customer ID:', error);
    throw new Error(error.message);
  }
}

export async function getUserByStripeCustomerId(stripeCustomerId: string): Promise<User | null> {
  try {
    const user = await prisma.user.findUnique({
      where: {
        stripeCustomerId,
      },
    });

    return user;
  } catch (error: any) {
    console.error('Error getting user by Stripe customer ID:', error);
    throw new Error(error.message);
  }
}

export async function getAllActiveSubscriptions(): Promise<Subscription[]> {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: {
        status: {
          in: ['active', 'trialing', 'past_due'],
        },
      },
      include: {
        user: true,
        price: {
          include: {
            product: true,
          },
        },
      },
    });

    return subscriptions;
  } catch (error: any) {
    console.error('Error getting active subscriptions:', error);
    throw new Error(error.message);
  }
}

export async function processSubscriptionRenewal(
  stripeSubscriptionId: string,
  creditsToAdd: number
): Promise<{ subscription: Subscription; user: User } | null> {
  try {
    const subscription = await getSubscriptionByStripeId(stripeSubscriptionId);
    if (!subscription) {
      throw new Error('Subscription not found');
    }

    // Add credits to user account
    const updatedUser = await prisma.user.update({
      where: {
        UserID: subscription.userId,
      },
      data: {
        credits: {
          increment: creditsToAdd,
        },
        TotalCreditsBought: {
          increment: creditsToAdd,
        },
      },
    });

    return { subscription, user: updatedUser };
  } catch (error: any) {
    console.error('Error processing subscription renewal:', error);
    throw new Error(error.message);
  }
}

function generateSubscriptionId(): string {
  return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export async function getSubscriptionStats(): Promise<{
  totalActive: number;
  totalRevenue: number;
  subscriptionsByPlan: { [key: string]: number };
}> {
  try {
    const subscriptions = await getAllActiveSubscriptions();
    
    const stats = {
      totalActive: subscriptions.length,
      totalRevenue: 0,
      subscriptionsByPlan: {} as { [key: string]: number },
    };

    subscriptions.forEach((sub) => {
      stats.totalRevenue += sub.price.unitAmount;
      const planName = `${sub.price.credits / 1000}K Credits`;
      stats.subscriptionsByPlan[planName] = (stats.subscriptionsByPlan[planName] || 0) + 1;
    });

    return stats;
  } catch (error: any) {
    console.error('Error getting subscription stats:', error);
    throw new Error(error.message);
  }
}
